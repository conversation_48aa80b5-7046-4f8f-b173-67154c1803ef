#!/bin/bash

# Record Ranger ML Pipeline - Minikube Setup Script
# This script sets up a local Minikube cluster with all required addons and configurations

set -e

echo "🚀 Setting up Record Ranger ML Pipeline Local Development Environment"
echo "=================================================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

command -v docker >/dev/null 2>&1 || { echo "❌ Docker is required but not installed. Aborting." >&2; exit 1; }
command -v minikube >/dev/null 2>&1 || { echo "❌ Minikube is required but not installed. Aborting." >&2; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "❌ kubectl is required but not installed. Aborting." >&2; exit 1; }

echo "✅ All prerequisites found"

# Configuration
CLUSTER_NAME="record-ranger-local"
MEMORY="8192"
CPUS="4"
DISK_SIZE="50g"
KUBERNETES_VERSION="v1.28.3"

echo "🔧 Minikube Configuration:"
echo "   Cluster Name: $CLUSTER_NAME"
echo "   Memory: ${MEMORY}MB"
echo "   CPUs: $CPUS"
echo "   Disk Size: $DISK_SIZE"
echo "   Kubernetes Version: $KUBERNETES_VERSION"

# Start Minikube
echo "🏗️  Starting Minikube cluster..."
minikube start \
  --profile=$CLUSTER_NAME \
  --memory=$MEMORY \
  --cpus=$CPUS \
  --disk-size=$DISK_SIZE \
  --kubernetes-version=$KUBERNETES_VERSION \
  --driver=docker \
  --addons=ingress,dashboard,metrics-server,registry

# Set kubectl context
echo "🔗 Setting kubectl context..."
kubectl config use-context $CLUSTER_NAME

# Enable required addons
echo "🔌 Enabling additional addons..."
minikube addons enable ingress --profile=$CLUSTER_NAME
minikube addons enable dashboard --profile=$CLUSTER_NAME
minikube addons enable metrics-server --profile=$CLUSTER_NAME
minikube addons enable registry --profile=$CLUSTER_NAME
minikube addons enable storage-provisioner --profile=$CLUSTER_NAME

# Create namespaces
echo "📁 Creating namespaces..."
kubectl create namespace infrastructure --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace ml-pipeline --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace qa-services --dry-run=client -o yaml | kubectl apply -f -

# Label namespaces
kubectl label namespace infrastructure app=record-ranger-infrastructure --overwrite
kubectl label namespace ml-pipeline app=record-ranger-ml-pipeline --overwrite
kubectl label namespace qa-services app=record-ranger-qa-services --overwrite

# Setup local Docker registry access
echo "🐳 Setting up local Docker registry access..."

# Wait for registry service to be ready
echo "⏳ Waiting for registry service to be ready..."
for i in {1..60}; do
    if kubectl get service registry --namespace kube-system >/dev/null 2>&1; then
        echo "✅ Registry service is ready"
        break
    fi
    sleep 2
done

if ! kubectl get service registry --namespace kube-system >/dev/null 2>&1; then
    echo "❌ Registry service not found after 2 minutes"
    echo "   Please check if the registry addon is properly enabled"
    exit 1
fi

# Get registry service details
REGISTRY_CLUSTER_IP=$(kubectl get service registry --namespace kube-system -o jsonpath='{.spec.clusterIP}')
REGISTRY_SERVICE_PORT=$(kubectl get service registry --namespace kube-system -o jsonpath='{.spec.ports[0].port}')
MINIKUBE_IP=$(minikube ip --profile=$CLUSTER_NAME)

# For Minikube registry, we use localhost with port forwarding
# The registry runs as a ClusterIP service, so we need port forwarding to access it
REGISTRY_HOST="localhost"
REGISTRY_PORT="5000"

echo "📝 Local Docker Registry Information:"
echo "   Registry Service IP: $REGISTRY_CLUSTER_IP"
echo "   Registry Service Port: $REGISTRY_SERVICE_PORT"
echo "   External Access URL: $REGISTRY_HOST:$REGISTRY_PORT"
echo "   Minikube IP: $MINIKUBE_IP"
echo ""
echo "📋 Registry Access Methods:"
echo "   1. Port Forwarding (Recommended for external access):"
echo "      kubectl port-forward --namespace kube-system service/registry $REGISTRY_PORT:$REGISTRY_SERVICE_PORT"
echo "   2. Direct cluster access (from within cluster):"
echo "      registry.kube-system.svc.cluster.local:$REGISTRY_SERVICE_PORT"
echo ""
echo "🐳 Docker Usage:"
echo "   Tag: docker tag <image> $REGISTRY_HOST:$REGISTRY_PORT/<image>"
echo "   Push: docker push $REGISTRY_HOST:$REGISTRY_PORT/<image>"
echo ""
echo "ℹ️  For local development, images built with 'eval \$(minikube docker-env)'"
echo "   are directly available to Kubernetes without pushing to registry."

# Create registry configuration
mkdir -p config/local-registry
cat > config/local-registry/registry-config.env << EOF
# Registry Configuration for Record Ranger ML Pipeline
# Generated on $(date)

# External access configuration (requires port forwarding)
REGISTRY_HOST=$REGISTRY_HOST
REGISTRY_PORT=$REGISTRY_PORT
REGISTRY_URL=$REGISTRY_HOST:$REGISTRY_PORT

# Internal cluster configuration
REGISTRY_CLUSTER_IP=$REGISTRY_CLUSTER_IP
REGISTRY_SERVICE_PORT=$REGISTRY_SERVICE_PORT
REGISTRY_INTERNAL_URL=registry.kube-system.svc.cluster.local:$REGISTRY_SERVICE_PORT

# Minikube details
MINIKUBE_IP=$MINIKUBE_IP
CLUSTER_NAME=$CLUSTER_NAME

# Port forwarding command
PORT_FORWARD_COMMAND="kubectl port-forward --namespace kube-system service/registry $REGISTRY_PORT:$REGISTRY_SERVICE_PORT"
EOF

echo "💾 Registry configuration saved to config/local-registry/registry-config.env"

echo "✅ Minikube setup complete!"
echo ""
echo "🎯 Next Steps:"
echo "   1. Run './start-environment.sh' to deploy all services"
echo "   2. Access Minikube dashboard: minikube dashboard --profile=$CLUSTER_NAME"
echo "   3. Check cluster status: kubectl get nodes"
echo ""
echo "📊 Cluster Information:"
echo "   Profile: $CLUSTER_NAME"
echo "   Dashboard: minikube dashboard --profile=$CLUSTER_NAME"
echo "   Registry: $MINIKUBE_IP:$REGISTRY_PORT"
echo "   Status: minikube status --profile=$CLUSTER_NAME"
