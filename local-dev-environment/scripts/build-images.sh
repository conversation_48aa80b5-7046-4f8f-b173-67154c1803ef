#!/bin/bash

# Record Ranger ML Pipeline - Build Images Script
# This script builds all Docker images for local development

set -e

echo "🐳 Building Record Ranger ML Pipeline Docker Images"
echo "=================================================="

# Configuration
CLUSTER_NAME="record-ranger-local"
REGISTRY_CONFIG="config/local-registry/registry-config.env"
ML_PIPELINE_REPO="/home/<USER>/dev/github/atomadvantage/aa_record_ranger_ml_pipeline"

# Load registry configuration
if [ -f "$REGISTRY_CONFIG" ]; then
    source "$REGISTRY_CONFIG"
    echo "📝 Using registry: $REGISTRY_URL"
else
    echo "❌ Registry configuration not found. Please run setup-minikube.sh first."
    exit 1
fi

# Check if Minikube is running
echo "🔍 Checking Minikube status..."
if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
    echo "❌ Minikube cluster '$CLUSTER_NAME' is not running."
    echo "   Please run './setup-minikube.sh' first."
    exit 1
fi

# Set Docker environment to use Minikube's Docker daemon
echo "🔧 Setting Docker environment..."
eval $(minikube docker-env --profile=$CLUSTER_NAME)

# Check if ML pipeline repository exists
if [ ! -d "$ML_PIPELINE_REPO" ]; then
    echo "❌ ML Pipeline repository not found at: $ML_PIPELINE_REPO"
    echo "   Please ensure the aa_record_ranger_ml_pipeline repository is cloned."
    exit 1
fi

# Define service mappings: local-dev-name -> actual-repo-directory
declare -A SERVICE_MAPPINGS=(
    ["downloader"]="downloader"
    ["classifier"]="classifier"
    ["splitter"]="splitter"
    ["metadata-extractor"]="metadata_extractor"
    ["metadata-post-processor"]="metadata_postprocessor"
    ["validate-route"]="validate_and_route"
    ["uploader"]="uploader"
    ["qa-post-processor"]="qa_post_processor"
)

# Services that don't have implementations in the ML pipeline repo
declare -a PLACEHOLDER_SERVICES=(
    "llm-server"
    "qa-backend"
    "qa-frontend"
)

# Build each service
echo "🏗️  Building Docker images..."

# Function to create fallback service images
create_fallback_service_image() {
    local service_name="$1"
    local repo_dir="$2"

    docker build -t "$service_name:latest" - << EOF
FROM python:3.10-slim
WORKDIR /app
RUN pip install --no-cache-dir fastapi uvicorn pika psycopg2-binary minio sqlalchemy
RUN echo 'from fastapi import FastAPI
import sys
app = FastAPI()

@app.get("/health")
def health():
    return {"status": "healthy", "service": "$service_name", "type": "fallback"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)' > main.py

CMD ["python", "main.py"]
EOF
}

# Build services with actual implementations
for service in "${!SERVICE_MAPPINGS[@]}"; do
    repo_dir="${SERVICE_MAPPINGS[$service]}"
    dockerfile_path="$ML_PIPELINE_REPO/$repo_dir/Dockerfile"

    echo "   Building $service from $repo_dir..."

    if [ -f "$dockerfile_path" ]; then
        # Create a local-friendly Dockerfile by replacing private ECR images with public ones
        temp_dockerfile=$(mktemp)

        # Read the original Dockerfile and replace ECR images with public equivalents
        sed 's|112623991000\.dkr\.ecr\.us-east-2\.amazonaws\.com/python:3\.10\.15|python:3.10.15|g;
             s|112623991000\.dkr\.ecr\.us-east-2\.amazonaws\.com/paddle-gpu:2\.6\.1-gpu-cuda11\.7-cudnn8\.4-trt8\.4|python:3.10.15|g' \
             "$dockerfile_path" > "$temp_dockerfile"

        # Build using the modified Dockerfile from the ML pipeline repo root
        docker build -t "$service:latest" -f "$temp_dockerfile" "$ML_PIPELINE_REPO/" || {
            echo "⚠️  Failed to build $service from actual Dockerfile, creating fallback..."
            # Create a simple fallback image
            create_fallback_service_image "$service" "$repo_dir"
        }

        # Clean up temp file
        rm -f "$temp_dockerfile"
    else
        echo "⚠️  Dockerfile not found at $dockerfile_path, creating fallback..."
        create_fallback_service_image "$service" "$repo_dir"
    fi

    # Tag for local registry
    docker tag "$service:latest" "localhost:5000/$service:latest"
    echo "   ✅ $service built successfully"
done



# Build placeholder services
for service in "${PLACEHOLDER_SERVICES[@]}"; do
    echo "   Building placeholder $service..."

    # Create appropriate placeholder based on service type
    if [[ "$service" == "llm-server" ]]; then
        # LLM server placeholder with basic ML dependencies
        docker build -t "$service:latest" - << 'EOF'
FROM python:3.10-slim
WORKDIR /app
RUN pip install --no-cache-dir fastapi uvicorn
RUN echo 'from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/health")
def health():
    return {"status": "healthy", "service": "llm-server"}

@app.post("/generate")
def generate(prompt: dict):
    return {"response": "LLM placeholder response"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)' > main.py
EXPOSE 8080
CMD ["python", "main.py"]
EOF
    elif [[ "$service" == "qa-backend" ]]; then
        # QA backend placeholder
        docker build -t "$service:latest" - << 'EOF'
FROM python:3.10-slim
WORKDIR /app
RUN pip install --no-cache-dir fastapi uvicorn sqlalchemy psycopg2-binary
RUN echo 'from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/health")
def health():
    return {"status": "healthy", "service": "qa-backend"}

@app.get("/api/documents")
def get_documents():
    return {"documents": []}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)' > main.py
EXPOSE 8000
CMD ["python", "main.py"]
EOF
    elif [[ "$service" == "qa-frontend" ]]; then
        # QA frontend placeholder
        docker build -t "$service:latest" - << 'EOF'
FROM node:16-alpine
WORKDIR /app
RUN echo '{"name": "qa-frontend", "version": "1.0.0", "scripts": {"start": "node server.js"}}' > package.json
RUN echo 'const http = require("http");
const server = http.createServer((req, res) => {
  res.writeHead(200, {"Content-Type": "text/html"});
  res.end("<h1>QA Frontend Placeholder</h1><p>Service is running</p>");
});
server.listen(3000, () => console.log("QA Frontend running on port 3000"));' > server.js
EXPOSE 3000
CMD ["npm", "start"]
EOF
    fi

    # Tag for local registry
    docker tag "$service:latest" "localhost:5000/$service:latest"
    echo "   ✅ $service placeholder built successfully"
done

# Push images to local registry (optional)
echo ""
echo "ℹ️  Images are already available in Minikube's Docker daemon."
echo "   For local development, Kubernetes can use these images directly."
echo ""
read -p "📤 Push images to local registry anyway? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📤 Setting up registry access and pushing images..."

    # Check if port forwarding is already active
    if ! curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
        echo "🔗 Setting up port forwarding to registry..."
        kubectl port-forward --namespace kube-system service/registry $REGISTRY_PORT:80 &
        PORT_FORWARD_PID=$!

        # Wait for port forwarding to be ready
        echo "⏳ Waiting for registry to be accessible..."
        for i in {1..30}; do
            if curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
                echo "✅ Registry is accessible"
                break
            fi
            sleep 1
        done

        if ! curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
            echo "❌ Failed to access registry after port forwarding"
            kill $PORT_FORWARD_PID 2>/dev/null
            exit 1
        fi
    fi

    # Push images
    for service in "${!SERVICE_MAPPINGS[@]}" "${PLACEHOLDER_SERVICES[@]}"; do
        echo "   Pushing $service..."
        docker push "localhost:5000/$service:latest" || echo "   ⚠️  Failed to push $service"
    done

    # Clean up port forwarding if we started it
    if [ ! -z "$PORT_FORWARD_PID" ]; then
        echo "🧹 Cleaning up port forwarding..."
        kill $PORT_FORWARD_PID 2>/dev/null
    fi
else
    echo "⏭️  Skipping registry push. Images are available in Minikube's Docker daemon."
fi

echo "✅ Image building completed!"
echo ""
echo "📋 Built Images:"
echo "ML Pipeline Services (from actual codebase):"
for service in "${!SERVICE_MAPPINGS[@]}"; do
    echo "   - $service:latest (from ${SERVICE_MAPPINGS[$service]})"
done
echo ""
echo "Placeholder Services:"
for service in "${PLACEHOLDER_SERVICES[@]}"; do
    echo "   - $service:latest (placeholder)"
done
echo ""
echo "🎯 Next Steps:"
echo "   1. Start the environment: ./start-environment.sh"
echo "   2. Check image status: docker images"
echo ""
