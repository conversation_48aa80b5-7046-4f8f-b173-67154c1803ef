#!/bin/bash

# Record Ranger ML Pipeline - Registry Validation Script
# This script validates registry connectivity and provides diagnostic information

set -e

echo "🔍 Registry Validation and Diagnostics"
echo "======================================"

# Configuration
CLUSTER_NAME="record-ranger-local"
REGISTRY_CONFIG="config/local-registry/registry-config.env"
PID_FILE="/tmp/registry-port-forward.pid"

# Load registry configuration if available
if [ -f "$REGISTRY_CONFIG" ]; then
    source "$REGISTRY_CONFIG"
    echo "📝 Loaded registry configuration from $REGISTRY_CONFIG"
    echo "   Registry URL: $REGISTRY_URL"
    echo "   Cluster IP: $REGISTRY_CLUSTER_IP"
    echo "   Service Port: $REGISTRY_SERVICE_PORT"
else
    echo "❌ Registry configuration not found at $REGISTRY_CONFIG"
    echo "   Please run setup-minikube.sh first"
    exit 1
fi

# Function to check Minikube status
check_minikube_status() {
    echo ""
    echo "🔍 Checking Minikube status..."
    
    if ! command -v minikube >/dev/null 2>&1; then
        echo "❌ Minikube not found in PATH"
        return 1
    fi
    
    if ! minikube status --profile=$CLUSTER_NAME >/dev/null 2>&1; then
        echo "❌ Minikube cluster '$CLUSTER_NAME' is not running"
        echo "   Run: minikube start --profile=$CLUSTER_NAME"
        return 1
    fi
    
    echo "✅ Minikube cluster '$CLUSTER_NAME' is running"
    return 0
}

# Function to check registry service
check_registry_service() {
    echo ""
    echo "🔍 Checking registry service..."
    
    if ! kubectl get service registry --namespace kube-system >/dev/null 2>&1; then
        echo "❌ Registry service not found in kube-system namespace"
        echo "   Run: minikube addons enable registry --profile=$CLUSTER_NAME"
        return 1
    fi
    
    local current_cluster_ip=$(kubectl get service registry --namespace kube-system -o jsonpath='{.spec.clusterIP}')
    local current_service_port=$(kubectl get service registry --namespace kube-system -o jsonpath='{.spec.ports[0].port}')
    
    echo "✅ Registry service found"
    echo "   Current Cluster IP: $current_cluster_ip"
    echo "   Current Service Port: $current_service_port"
    
    # Check if configuration matches current service
    if [ "$REGISTRY_CLUSTER_IP" != "$current_cluster_ip" ] || [ "$REGISTRY_SERVICE_PORT" != "$current_service_port" ]; then
        echo "⚠️  Registry configuration mismatch detected!"
        echo "   Config Cluster IP: $REGISTRY_CLUSTER_IP (current: $current_cluster_ip)"
        echo "   Config Service Port: $REGISTRY_SERVICE_PORT (current: $current_service_port)"
        echo "   Consider running setup-minikube.sh to update configuration"
    fi
    
    return 0
}

# Function to check registry pods
check_registry_pods() {
    echo ""
    echo "🔍 Checking registry pods..."
    
    local registry_pods=$(kubectl get pods --namespace kube-system -l actual-registry=true --no-headers 2>/dev/null | wc -l)
    
    if [ "$registry_pods" -eq 0 ]; then
        echo "❌ No registry pods found"
        echo "   The registry addon may not be properly enabled"
        return 1
    fi
    
    echo "✅ Found $registry_pods registry pod(s)"
    kubectl get pods --namespace kube-system -l actual-registry=true
    
    # Check pod status
    local running_pods=$(kubectl get pods --namespace kube-system -l actual-registry=true --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
    
    if [ "$running_pods" -eq 0 ]; then
        echo "❌ No registry pods are running"
        return 1
    fi
    
    echo "✅ $running_pods registry pod(s) running"
    return 0
}

# Function to check port forwarding
check_port_forwarding() {
    echo ""
    echo "🔍 Checking port forwarding..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "✅ Port forwarding process running (PID: $pid)"
            
            # Check if port is actually accessible
            if curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
                echo "✅ Registry accessible via port forwarding"
                return 0
            else
                echo "❌ Port forwarding running but registry not accessible"
                return 1
            fi
        else
            echo "⚠️  Stale PID file found, cleaning up"
            rm -f "$PID_FILE"
        fi
    fi
    
    echo "❌ Port forwarding not active"
    return 1
}

# Function to test registry connectivity
test_registry_connectivity() {
    echo ""
    echo "🔍 Testing registry connectivity..."
    
    # Test direct access
    echo "   Testing direct access to $REGISTRY_URL..."
    if curl -s "http://$REGISTRY_URL/v2/" >/dev/null 2>&1; then
        echo "✅ Registry accessible at $REGISTRY_URL"
        
        # Test registry API
        local version=$(curl -s "http://$REGISTRY_URL/v2/" | grep -o '"version":"[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "unknown")
        echo "   Registry version: $version"
        
        return 0
    else
        echo "❌ Registry not accessible at $REGISTRY_URL"
        return 1
    fi
}

# Function to provide recommendations
provide_recommendations() {
    echo ""
    echo "💡 Recommendations:"
    echo "=================="
    
    if ! check_minikube_status >/dev/null 2>&1; then
        echo "1. Start Minikube cluster:"
        echo "   ./scripts/setup-minikube.sh"
        return
    fi
    
    if ! check_registry_service >/dev/null 2>&1; then
        echo "1. Enable registry addon:"
        echo "   minikube addons enable registry --profile=$CLUSTER_NAME"
        return
    fi
    
    if ! check_registry_pods >/dev/null 2>&1; then
        echo "1. Check registry addon status:"
        echo "   minikube addons list --profile=$CLUSTER_NAME | grep registry"
        echo "2. Restart registry addon:"
        echo "   minikube addons disable registry --profile=$CLUSTER_NAME"
        echo "   minikube addons enable registry --profile=$CLUSTER_NAME"
        return
    fi
    
    if ! check_port_forwarding >/dev/null 2>&1; then
        echo "1. Start port forwarding:"
        echo "   ./scripts/registry-port-forward.sh start"
        echo "2. Or use the build script which handles this automatically:"
        echo "   ./scripts/build-images.sh"
        return
    fi
    
    echo "✅ Registry appears to be properly configured and accessible"
    echo "   You can now build and push images using:"
    echo "   ./scripts/build-images.sh"
}

# Main validation flow
main() {
    local exit_code=0
    
    check_minikube_status || exit_code=1
    check_registry_service || exit_code=1
    check_registry_pods || exit_code=1
    check_port_forwarding || exit_code=1
    test_registry_connectivity || exit_code=1
    
    provide_recommendations
    
    echo ""
    if [ $exit_code -eq 0 ]; then
        echo "✅ Registry validation completed successfully"
    else
        echo "❌ Registry validation found issues (see recommendations above)"
    fi
    
    exit $exit_code
}

# Run main function
main "$@"
